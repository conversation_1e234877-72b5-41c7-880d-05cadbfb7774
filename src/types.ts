import * as L from 'leaflet';

export enum LayerType {
  WMS = 'wms',
  GEOJSON = 'geojson',
  WFS = 'wfs',
}

export interface BaseLayer {
  id: string;
  name: string;
  visible: boolean;
  parent?: GroupLayer;
}

export interface GeoLayer extends BaseLayer {
  type: LayerType;
  url?: string;
  layer?: string;
  layer_instance?: L.Layer;
  legend_url?: string;
  abstract?: string;
  opacity: number;
  zIndex: number;
  originalName: string;
  groupPath?: string;
}

export interface GroupLayer extends BaseLayer {
  children: (GeoLayer | GroupLayer)[];
  collapsed: boolean;
  level: number;
}

export interface LegendItem {
  id: string;
  name: string;
  collapsed: boolean;
  legend_url?: string;
  opacity: number;
  symbols: Array<{
    color?: string;
    icon?: string;
    label: string;
  }>;
  legendState: {     loaded: boolean;     error: boolean;     loading: boolean };
}
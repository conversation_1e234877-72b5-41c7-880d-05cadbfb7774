// Test script to verify reactivity without updateCounter
// This can be run in the browser console to test the reactive system

console.log('Testing reactivity without updateCounter...');

// Test function to verify layer reactivity
function testLayerReactivity() {
  console.log('=== Testing Layer Reactivity ===');
  
  if (!window.layerManager) {
    console.error('LayerManager not available. Make sure the app is loaded.');
    return;
  }
  
  const layerManager = window.layerManager;
  
  // Test 1: Check initial state
  console.log('Initial layers:', layerManager.layers.value);
  console.log('Initial root groups:', layerManager.rootGroups.value);
  console.log('Initial ungrouped layers:', layerManager.ungroupedLayers.value);
  
  // Test 2: Toggle layer visibility
  const firstLayer = layerManager.layers.value[0];
  if (firstLayer) {
    console.log(`Testing layer visibility toggle for: ${firstLayer.name}`);
    const initialVisibility = firstLayer.visible;
    console.log(`Initial visibility: ${initialVisibility}`);
    
    // Toggle visibility
    layerManager.toggleLayerVisibility(firstLayer.id);
    console.log(`New visibility: ${firstLayer.visible}`);
    console.log(`Visibility changed: ${initialVisibility !== firstLayer.visible}`);
    
    // Toggle back
    layerManager.toggleLayerVisibility(firstLayer.id);
    console.log(`Restored visibility: ${firstLayer.visible}`);
    console.log(`Visibility restored: ${initialVisibility === firstLayer.visible}`);
  }
  
  // Test 3: Toggle group collapse
  const firstGroup = layerManager.rootGroups.value[0];
  if (firstGroup) {
    console.log(`Testing group collapse toggle for: ${firstGroup.name}`);
    const initialCollapsed = firstGroup.collapsed;
    console.log(`Initial collapsed state: ${initialCollapsed}`);
    
    // Toggle collapse
    layerManager.toggleGroupCollapse(firstGroup.id);
    console.log(`New collapsed state: ${firstGroup.collapsed}`);
    console.log(`Collapsed state changed: ${initialCollapsed !== firstGroup.collapsed}`);
    
    // Toggle back
    layerManager.toggleGroupCollapse(firstGroup.id);
    console.log(`Restored collapsed state: ${firstGroup.collapsed}`);
    console.log(`Collapsed state restored: ${initialCollapsed === firstGroup.collapsed}`);
  }
  
  // Test 4: Update layer opacity
  if (firstLayer) {
    console.log(`Testing layer opacity update for: ${firstLayer.name}`);
    const initialOpacity = firstLayer.opacity;
    console.log(`Initial opacity: ${initialOpacity}`);
    
    // Update opacity
    const newOpacity = 0.8;
    layerManager.updateLayerOpacity(firstLayer.id, newOpacity);
    console.log(`New opacity: ${firstLayer.opacity}`);
    console.log(`Opacity changed: ${firstLayer.opacity === newOpacity}`);
    
    // Restore opacity
    layerManager.updateLayerOpacity(firstLayer.id, initialOpacity);
    console.log(`Restored opacity: ${firstLayer.opacity}`);
    console.log(`Opacity restored: ${firstLayer.opacity === initialOpacity}`);
  }
  
  console.log('=== Reactivity Test Complete ===');
}

// Test function to verify group reactivity
function testGroupReactivity() {
  console.log('=== Testing Group Reactivity ===');
  
  if (!window.layerManager) {
    console.error('LayerManager not available. Make sure the app is loaded.');
    return;
  }
  
  const layerManager = window.layerManager;
  const firstGroup = layerManager.rootGroups.value[0];
  
  if (firstGroup) {
    console.log(`Testing group visibility toggle for: ${firstGroup.name}`);
    const initialVisibility = firstGroup.visible;
    console.log(`Initial group visibility: ${initialVisibility}`);
    
    // Toggle group visibility
    layerManager.toggleGroupVisibility(firstGroup.id);
    console.log(`New group visibility: ${firstGroup.visible}`);
    console.log(`Group visibility changed: ${initialVisibility !== firstGroup.visible}`);
    
    // Check children visibility
    console.log('Children visibility after group toggle:');
    firstGroup.children.forEach(child => {
      if ('children' in child) {
        console.log(`  Group ${child.name}: ${child.visible}`);
      } else {
        console.log(`  Layer ${child.name}: ${child.visible}`);
      }
    });
    
    // Toggle back
    layerManager.toggleGroupVisibility(firstGroup.id);
    console.log(`Restored group visibility: ${firstGroup.visible}`);
  }
  
  console.log('=== Group Reactivity Test Complete ===');
}

// Export test functions to global scope
window.testLayerReactivity = testLayerReactivity;
window.testGroupReactivity = testGroupReactivity;

console.log('Test functions available:');
console.log('- window.testLayerReactivity()');
console.log('- window.testGroupReactivity()');
console.log('Run these functions in the browser console to test reactivity.');

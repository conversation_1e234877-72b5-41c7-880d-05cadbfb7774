<template>
  <div
    class="layer-item"
    :style="{ paddingLeft: `${(layer.parent?.level || 0) * 16 + 32}px` }"
  >
    <div class="layer-content">
      <label class="layer-label">
        <input
          type="checkbox"
          :checked="isLayerVisible"
          @change="toggleVisibility"
          class="layer-checkbox"
        />
        <span
          class="layer-name"
          :title="layer.abstract || layer.name"
        >
          {{ layer.name }}
        </span>
      </label>

      <!-- Info button inline -->
      <button
        v-if="layer.abstract"
        ref="infoButtonRef"
        class="info-button"
        @click="togglePopover"
        @mouseenter="showPopoverOnHover"
        @mouseleave="hidePopoverOnHover"
        :aria-label="`Show info for ${layer.name}`"
        :aria-expanded="showPopover"
      >
        <i class="fas fa-info-circle"></i>
      </button>
    </div>

    <!-- Popover for layer info -->
    <div
      v-if="showPopover && layer.abstract"
      ref="popoverRef"
      class="layer-popover"
      :style="popoverStyle"
      @click.stop
    >
      <div class="popover-content">
        <p>{{ layer.abstract }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';
import type { GeoLayer } from '../types';

// Props
const props = defineProps<{
  layer: GeoLayer;
}>();

// Emits
const emit = defineEmits<{
  'toggle-visibility': [layerId: string, visible?: boolean];
}>();

// Template refs
const infoButtonRef = ref<HTMLElement>();
const popoverRef = ref<HTMLElement>();

// Local state
const showPopover = ref(false);
const isHovering = ref(false);
const popoverStyle = ref({});

// Reactive visibility state
const isLayerVisible = computed(() => {
  return props.layer.visible;
});

// Event handlers
const toggleVisibility = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit('toggle-visibility', props.layer.id, target.checked);
};

const togglePopover = () => {
  showPopover.value = !showPopover.value;
  if (showPopover.value) {
    nextTick(() => {
      calculatePopoverPosition();
    });
  }
};

const showPopoverOnHover = () => {
  // Only show on hover for desktop (non-touch devices)
  if (!('ontouchstart' in window)) {
    isHovering.value = true;
    showPopover.value = true;
    nextTick(() => {
      calculatePopoverPosition();
    });
  }
};

const hidePopoverOnHover = () => {
  if (!('ontouchstart' in window)) {
    isHovering.value = false;
    // Small delay to allow moving to popover
    setTimeout(() => {
      if (!isHovering.value) {
        showPopover.value = false;
      }
    }, 100);
  }
};

const calculatePopoverPosition = () => {
  if (!infoButtonRef.value || !popoverRef.value) return;

  const button = infoButtonRef.value;
  const popover = popoverRef.value;
  const buttonRect = button.getBoundingClientRect();
  const popoverRect = popover.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let left = buttonRect.left + buttonRect.width + 8;
  let top = buttonRect.top - (popoverRect.height / 2) + (buttonRect.height / 2);

  // Adjust if popover would go off-screen horizontally
  if (left + popoverRect.width > viewportWidth - 10) {
    left = buttonRect.left - popoverRect.width - 8;
  }

  // Adjust if popover would go off-screen vertically
  if (top < 10) {
    top = 10;
  } else if (top + popoverRect.height > viewportHeight - 10) {
    top = viewportHeight - popoverRect.height - 10;
  }

  popoverStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    zIndex: 10000
  };
};

const handleClickOutside = (event: Event) => {
  if (
    showPopover.value &&
    infoButtonRef.value &&
    popoverRef.value &&
    !infoButtonRef.value.contains(event.target as Node) &&
    !popoverRef.value.contains(event.target as Node)
  ) {
    showPopover.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.layer-item {
  padding: 6px 8px;
  border-bottom: 1px solid #f1f3f4;
  background-color: #ffffff;
  transition: background-color 0.2s ease;
  position: relative;
}

.layer-item:hover {
  background-color: #f8f9fa;
}

.layer-item:last-child {
  border-bottom: none;
}

.layer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.layer-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.layer-checkbox {
  margin-right: 8px;
  cursor: pointer;
  flex-shrink: 0;
}

.layer-name {
  font-size: 13px;
  color: #495057;
  user-select: none;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  color: #6c757d;
  transition: all 0.2s ease;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-button:hover {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
  transform: scale(1.1);
}

.info-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}

.layer-popover {
  position: fixed;
  max-width: 280px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  animation: popoverFadeIn 0.2s ease-out;
}

.popover-content {
  padding: 12px;
}

.popover-content p {
  margin: 0;
  font-size: 12px;
  color: #495057;
  line-height: 1.4;
}

@keyframes popoverFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Accessibility improvements */
.layer-checkbox:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .layer-popover {
    max-width: 240px;
  }

  .popover-content {
    padding: 10px;
  }

  .popover-content p {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .layer-item {
    padding: 4px 6px;
  }

  .layer-name {
    font-size: 12px;
  }

  .info-button {
    width: 20px;
    height: 20px;
  }

  .layer-popover {
    max-width: 200px;
  }

  .popover-content {
    padding: 8px;
  }

  .popover-content p {
    font-size: 10px;
  }
}
</style>

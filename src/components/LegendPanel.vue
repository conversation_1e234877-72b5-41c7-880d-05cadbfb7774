<template>
  <div 
    v-if="visible" 
    ref="panelRef"
    class="legend-panel"
    :class="{ dragging: isDragging }"
  >
    <div class="legend-header">
      <div
        class="drag-handle"
        @mousedown="startDrag"
        @touchstart="startDragTouch"
        title="Drag to move panel"
      >
        <i class="fas fa-grip-vertical"></i>
      </div>
      <h2>Leyenda</h2>
      <button
        class="close-button"
        @click="$emit('close')"
        :aria-label="'Close legend panel'"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="legend-content">
      <div v-if="visibleLayers?.length === 0" class="no-legend">
        No hay capas visibles para mostrar en la leyenda.
      </div>
      <div v-else class="legend-items">
        <LegendItem
          v-for="layer in visibleLayers"
          :key="layer.id"
          :legendItem="layer"
          :onImageError="onImageError"
          :onImageLoad="onImageLoad"
          :onImageLoadStart="onImageLoadStart"
          :updateOpacity="updateOpacity"
          :updateOpacityFromInput="updateOpacityFromInput"
          class="legend-item"
        >
        </LegendItem>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, reactive } from 'vue';
import { useDraggable } from '../composables/useDraggable';
import type { GeoLayer } from '../types';
import type { LayerManager } from '../layers';
import LegendItem from "./LegendItem.vue";

// Props and emits
const props = defineProps<{
  visible: boolean;
}>();

defineEmits<{
  close: [];
}>();

// Template refs
const panelRef = ref<HTMLElement>();

// Get services from injection
const getLayerManager = inject<() => LayerManager | null>('layerManager');

// Use composables
const { isDragging, startDrag, startDragTouch } = useDraggable(panelRef);

// Reactive state for legend image loading and errors
const legendImageStates = reactive<Record<string, {
  loaded: boolean;
  error: boolean;
  loading: boolean;
}>>({});

// Computed property for visible layers with enhanced legend state
const visibleLayers = computed(() => {
  const layerManager = getLayerManager?.();
  if (!layerManager?.layers?.value || !Array.isArray(layerManager.layers.value)) return [];
  return layerManager.layers.value.filter(layer => layer.visible).map(layer => ({
    ...layer,
    legendState: legendImageStates[layer.id] || { loaded: false, error: false, loading: false },
    collapsed: false,
  }));
});

// Initialize legend state for a layer
const initializeLegendState = (layerId: string) => {
  if (!legendImageStates[layerId]) {
    legendImageStates[layerId] = {
      loaded: false,
      error: false,
      loading: true
    };
  }
};

// Update opacity handlers
const updateOpacity = (layerId: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const opacity = parseFloat(target.value);
  const layerManager = getLayerManager?.();
  if (layerManager) {
    layerManager.updateLayerOpacity(layerId, opacity);
  }
};

const updateOpacityFromInput = (layerId: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const opacity = parseInt(target.value) / 100;
  const layerManager = getLayerManager?.();
  if (layerManager) {
    layerManager.updateLayerOpacity(layerId, opacity);
  }
};

// Reactive image event handlers
const onImageError = (layerId: string) => {
  legendImageStates[layerId] = {
    loaded: false,
    error: true,
    loading: false
  };
};

const onImageLoad = (layerId: string) => {
  legendImageStates[layerId] = {
    loaded: true,
    error: false,
    loading: false
  };
};

const onImageLoadStart = (layerId: string) => {
  initializeLegendState(layerId);
};
</script>

<style scoped>
.legend-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 300px;
  max-height: calc(100vh - 200px);
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.legend-panel.dragging {
  user-select: none;
  cursor: move;
}

.legend-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background-color: #0f5397;
  color: white;
  border-radius: 4px 4px 0 0;
  gap: 6px;
}

.drag-handle {
  cursor: move;
  color: rgba(255, 255, 255, 0.7);
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.drag-handle:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.legend-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.close-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: white;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.close-button:focus {
  outline: 2px solid #ffffff;
  outline-offset: 1px;
}

.legend-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.no-legend {
  text-align: center;
  padding: 16px;
  color: #6c757d;
  font-size: 13px;
}

.legend-items {
  display: flex;
  flex-direction: column;
}

.legend-item {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0;
}

.legend-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}


.legend-placeholder i {
  font-size: 24px;
  opacity: 0.5;
}

.legend-placeholder .fa-spinner {
  color: #0f5397;
  opacity: 1;
  animation: spin 1s linear infinite;
}

.legend-placeholder .fa-exclamation-triangle {
  color: #dc3545;
  opacity: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .legend-panel {
    width: 260px;
    right: 8px;
    top: 8px;
    max-height: calc(100vh - 150px);
  }
}

@media (max-width: 480px) {
  .legend-panel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
    top: 5px;
    max-height: calc(100vh - 100px);
  }

  .legend-content {
    padding: 8px;
  }
}
</style>

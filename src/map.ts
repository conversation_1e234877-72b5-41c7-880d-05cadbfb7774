import L from 'leaflet';
import { GeoLayer, LayerType } from './types';
import { config } from './config';

export class MapManager {
  private map: L.Map;
  private baseLayers: Record<string, L.TileLayer> = {};
  private layers: GeoLayer[] = [];

  constructor(mapElementId: string) {
    const ecuadorBounds = L.latLngBounds(
      [config.map.ecuadorBounds.southwest.lat, config.map.ecuadorBounds.southwest.lng],
      [config.map.ecuadorBounds.northeast.lat, config.map.ecuadorBounds.northeast.lng]
    );

    this.map = L.map(mapElementId, {
      center: [config.map.defaultCenter.lat, config.map.defaultCenter.lng],
      zoom: config.map.defaultZoom,
      zoomControl: false,
      maxBounds: ecuadorBounds,
      maxBoundsViscosity: config.map.maxBoundsViscosity,
      minZoom: config.map.minZoom,
    });
    this.zoomToHome();

    L.control.zoom({
      position: 'bottomleft'
    }).addTo(this.map);

    this.initializeBaseLayers();
    L.control.scale().addTo(this.map);
  }

  private initializeBaseLayers(): void {
    this.baseLayers['osm'] = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19,
    }).addTo(this.map);

    this.baseLayers['satellite'] = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
      attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
      maxZoom: 19,
    });

    this.baseLayers['topo'] = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
      attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)',
      maxZoom: 17,
    });

    L.control.layers(
      {
        'OpenStreetMap': this.baseLayers['osm'],
        'Satellite': this.baseLayers['satellite'],
        'Topographic': this.baseLayers['topo'],
      },
      {},
      { position: 'topright' }
    ).addTo(this.map);
  }

  public getMap(): L.Map {
    return this.map;
  }

  public addLayer(layer: GeoLayer): void {
    const existingLayerIndex = this.layers.findIndex(l => l.id === layer.id);
    if (existingLayerIndex !== -1) {
      this.removeLayer(layer.id);
    }
    this.layers.push(layer);
    if (layer.visible) {
      this.createAndAddLayer(layer);
    }
  }

  public removeLayer(layerId: string): void {
    const layerIndex = this.layers.findIndex(l => l.id === layerId);
    if (layerIndex !== -1) {
      const layer = this.layers[layerIndex];
      if (layer.layer_instance) {
        this.map.removeLayer(layer.layer_instance);
      }
      this.layers.splice(layerIndex, 1);
    }
  }

  public toggleLayerVisibility(layerId: string, visible?: boolean): void {
    const layer = this.layers.find(l => l.id === layerId);
    if (!layer) return;

    layer.visible = visible !== undefined ? visible : !layer.visible;

    if (layer.visible) {
      if (!layer.layer_instance) {
        this.createAndAddLayer(layer);
      } else {
        this.map.addLayer(layer.layer_instance);
        this.bringLayerToTop(layerId);
      }
    } else {
      if (layer.layer_instance) {
        this.map.removeLayer(layer.layer_instance);
      }
    }
  }

  public bringLayerToTop(layerId: string): void {
    const layer = this.layers.find(l => l.id === layerId);
    if (!layer) return;

    if (layer.layer_instance && 'bringToFront' in layer.layer_instance) {
      (layer.layer_instance as any).bringToFront();
    }
  }

  public updateLayerOpacity(layerId: string, opacity: number): void {
    const layer = this.layers.find(l => l.id === layerId);
    if (!layer || !layer.layer_instance) return;

    layer.opacity = opacity;

    if (layer.layer_instance) {
      if ('setOpacity' in layer.layer_instance) {
        (layer.layer_instance as any).setOpacity(opacity);
      } else if ('setStyle' in layer.layer_instance) {
        (layer.layer_instance as any).setStyle({ opacity });
      }
    }
  }

  private createAndAddLayer(layer: GeoLayer): void {
    if (layer.type === LayerType.WMS && layer.url && layer.layer) {
      const wmsLayer = L.tileLayer.wms(layer.url, {
        layers: layer.layer,
        format: 'image/png',
        transparent: true,
        opacity: layer.opacity || config.layer.defaultOpacity,
        zIndex: layer.zIndex || config.layer.defaultZIndex,
      });
      layer.layer_instance = wmsLayer;
      wmsLayer.addTo(this.map);
      this.bringLayerToTop(layer.id);
    } else if (layer.type === LayerType.GEOJSON && layer.url) {
      this.createGeoJSONLayer(layer);
    }
  }

  private async createGeoJSONLayer(layer: GeoLayer): Promise<void> {
    if (!layer.url) return;

    try {
      const response = await fetch(layer.url);
      const geoJsonData = await response.json();

      const geoJsonLayer = L.geoJSON(geoJsonData, {
        style: {
          color: '#3388ff',
          weight: 2,
          opacity: 0.8,
          fillOpacity: 0.6
        }
      });

      if (layer.opacity !== undefined) {
        geoJsonLayer.setStyle({ opacity: layer.opacity, fillOpacity: layer.opacity * 0.7 });
      }

      layer.layer_instance = geoJsonLayer;
      geoJsonLayer.addTo(this.map);
      this.bringLayerToTop(layer.id);
    } catch (error) {
      console.error(`Error loading GeoJSON layer ${layer.id}:`, error);
    }
  }

  public zoomToHome(): void {
    const homeBounds = L.latLngBounds(
      [config.map.homeBounds.southwest.lat, config.map.homeBounds.southwest.lng],
      [config.map.homeBounds.northeast.lat, config.map.homeBounds.northeast.lng]
    );
    this.map.fitBounds(homeBounds);
  }

  public destroy(): void {}
}